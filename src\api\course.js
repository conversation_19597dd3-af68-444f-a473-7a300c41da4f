import { http } from "@/utils/http";
/** 根据id查询 */
export const courseFindId = params => {
  return http.request(
    "get",
    "/organization/course/findById",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 查询课程列表 */
export const courseFindAll = params => {
  return http.request(
    "get",
    "/organization/course/findAll",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 新增课程 */
export const courseAdd = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/course/save",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/** 编辑课程 */
export const courseEdite = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/course/update",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/**课程删除 */
export const courseDelete = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/course/delete",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/**查询课期 */
// export const coursePeriodAll = params => {
//   return http.request(
//     "get",
//     "/organization/coursePeriod/findAll",
//     { params },
//     { isNeedEncrypt: true, isNeedToken: true }
//   );
// };
/**课期新增 */
export const coursePeriodAdd = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/coursePeriod/save",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/**课期删除 */
export const coursePeriodDelete = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/coursePeriod/delete",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/**课期编辑 */
export const coursePeriodEdite = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/coursePeriod/update",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/**课期上架 */
export const coursePeriodOnline = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/coursePeriod/online",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/**课期下架 */
export const coursePeriodOffline = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/coursePeriod/offline",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/** 新增实践点 */
export const complexAdd = data => {
  return http.request(
    "post",
    "/organization/complex/save",
    { data },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
// 实践点查询不分页
export const complexId = params => {
  return http.request(
    "get",
    "/organization/complex/findAllNotPage",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
// 课程分类查询不分页
export const courseTypeFind = params => {
  return http.request(
    "get",
    "/organization/courseType/findAll",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
// 领队讲师查询不分页
export const leaderLecturerFind = params => {
  return http.request(
    "get",
    "/organization/leaderLecturer/findAllNotPage",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
// 开启团购（课程定制）
export const periodOpenGroupOrder = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/coursePeriod/openGroupOrder",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 关闭团购（取消定制）
export const periodCloseGroupOrder = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/coursePeriod/closeGroupOrder",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
// 课期根据id查详情
export const coursePeriodFind = params => {
  return http.request(
    "get",
    "/organization/coursePeriod/findById",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 根据课期id获取团购单小程序码url */
export const getQRCodeUrl = params => {
  return http.request(
    "get",
    "/organization/coursePeriod/getQRCodeUrl",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 根据课期id获取团购单 */
export const getGroupBuying = params => {
  return http.request(
    "get",
    "/organization/coursePeriod/getGroupBuying",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 课程分类多级 */
export const findAllCourseType = params => {
  return http.request(
    "get",
    "/organization/courseType/findAllCourseType",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 机构登录 */
export const educationLogin = data => {
  return http.request(
    "post",
    "/organization/admin/login",
    // "/organization/admin/login",
    { data },
    { isNeedToken: false }
  );
};
// 课期批量删除
export const deletIds = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/organization/coursePeriod/batchDelete",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COURSE_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
