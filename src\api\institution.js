import { http } from "@/utils/http";

/** 登录 */
export const accountLogin = data => {
  return http.request(
    "post",
    // "/platform/admin/login",
    "/organization/admin/login",
    { data },
    { isNeedToken: false }
  );
};
/** 根据id查询 */
export const courseFindId = params => {
  return http.request(
    "get",
    "/platform/organization/findById",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 机构分页查询 */
export const courseFindAll = params => {
  return http.request(
    "get",
    "/platform/organization/findAll",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 新增机构 */
export const courseAdd = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/organization/save",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COMPLEX_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "创建了机构"
      }
    }
  );
};
/** 是否冻结 */
export const isFreeze = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/organization/isFreeze",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType:
          operateLog?.operateLogType || "ORGANIZATIONAL_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/** 编辑 */
export const organizationUpdate = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/organization/update",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType:
          operateLog?.operateLogType || "ORGANIZATIONAL_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "编辑了机构信息"
      }
    }
  );
};

// 机构管理-编辑-验证账号
export const verifyUsername = data => {
  return http.request(
    "post",
    "/platform/organizationAdmin/verifyUsername",
    { data },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
// 平台管理-修改密码
export const updatePassword = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/admin/updatePassword",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "修改了密码"
      }
    }
  );
};
// 机构管理-编辑-验证手机号
export const verifyPhone = data => {
  return http.request(
    "post",
    "/platform/admin/verifyPhone",
    { data },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
// 机构管理-编辑-重置密码
export const resetPassword = data => {
  return http.request(
    "post",
    "/platform/organizationAdmin/resetPassword",
    { data },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
// 实践点管理-是否冻结
export const complexIsFreeze = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/complex/isFreeze",
    { data },
    {
      isNeedEncrypt: true,
      isNeedToken: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};
/** 实践点管理分页查询 */
export const complexFindAll = params => {
  return http.request(
    "get",
    "/platform/complex/findAll",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 实践点管理id查询 */
export const complexFindById = params => {
  return http.request(
    "get",
    "/platform/complex/findById",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 获取机构财务 */
export const findOrganizationFinancial = params => {
  return http.request(
    "get",
    "/platform/financialRecord/findOrganizationFinancial",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 账务分页查询 */
export const financialRecordFindAll = params => {
  return http.request(
    "get",
    "/platform/financialRecord/findAll",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 账务导出 */
export const financialRecordExport = params => {
  return http.request(
    "get",
    "/platform/financialRecord/export",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
/** 关联订单 */
export const getOrderDetails = params => {
  return http.request(
    "get",
    "/platform/orders/getOrderDetails",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
//新增日志
export const operateLogSave = data => {
  return http.request(
    "post",
    "/platform/operateLog/save",
    { data },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
//查询日志
export const operateLogFindAll = params => {
  return http.request(
    "get",
    "/platform/operateLog/findAll",
    { params },
    { isNeedEncrypt: true }
  );
};
//首页
export const homeStatistics = params => {
  return http.request(
    "get",
    "/platform/home/<USER>",
    { params },
    { isNeedEncrypt: true }
  );
};
//根据id查询账号详情
export const adminFindById = params => {
  return http.request(
    "get",
    "/platform/admin/findById",
    { params },
    { isNeedEncrypt: true }
  );
};
//异步导入
export const asyncTask = params => {
  return http.request(
    "get",
    "/common/asyncTask/findById",
    { params },
    { isNeedEncrypt: true }
  );
};
// 领队讲师/编辑信息
export const editteaInfo = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/admin/update",
    { data },
    {
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "ACCOUNT_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "编辑了首页信息"
      }
    }
  );
};

/** 根据机构id查询银行账户 */
export const getBankAccount = (params = {}) => {
  return http.request(
    "get",
    "/platform/bank/findByOrganizationId",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
// 根据id查询机构详情
export const institutionFindById = params => {
  return http.request(
    "get",
    "/common/dict/findByParentId",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
