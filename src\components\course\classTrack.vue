<script setup>
import { onMounted, ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import { classTrackingFind } from "@/api/course.js";
import { requestTo } from "@/utils/http/tool";
import dayjs from "dayjs";
import { ImageThumbnail } from "@/utils/imageProxy";
const router = useRouter();
const route = useRoute();

const trackList = ref([]);
const tableData = ref([]);
// 课期上课跟踪查询
const findCoursePeriodList = async type => {
  const params = {
    coursePeriodId: route.query.periodId
  };
  let [err, res] = await requestTo(classTrackingFind(params));
  if (res) {
    console.log("🌳-----res-----", res);
    trackList.value = res;
  } else {
    console.log("🌵-----err-----", err);
  }
};
onMounted(() => {
  findCoursePeriodList();
});
</script>

<template>
  <div class="class-track">
    <div v-if="trackList?.length" class="content">
      <el-timeline>
        <el-timeline-item
          v-for="item in trackList"
          :key="item.id"
          :timestamp="item.timestamp"
        >
          <div class="timeLine_title">
            <span class="m-w">{{
              dayjs(item.timePoint).format("HH:mm:ss") || ""
            }}</span>
          </div>
          <div class="item_content">
            <div class="content">{{ item.content || "--" }}</div>
            <div v-if="item.files && item.files.length" class="img-box">
              <img
                v-for="(it, index) in item.files"
                :key="index"
                v-preview="{
                  url: it?.uploadFile?.url,
                  type: 'image',
                  name: it?.uploadFile?.fileName
                }"
                :src="ImageThumbnail(it?.uploadFile?.url, '250x')"
                alt=""
                class="img"
              >
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>

    <el-empty v-else description="暂无数据" />
  </div>
</template>

<style lang="scss" scoped>
.class-track {
  height: 100%;
  width: 100%;
  position: relative;

  .content {
    width: 100%;
    height: 94%;
    overflow-y: auto;
    padding: 10px;
    box-sizing: border-box;
    -ms-overflow-style: none;
    scrollbar-width: none;
    .timeLine_title {
      width: 100%;
      display: flex;
      align-items: center;
      gap: 20px;
    }
    .item_content {
      margin-top: 20px;
      width: 100%;
      padding-left: 30px;
      overflow-x: auto;
      -ms-overflow-style: none;
      scrollbar-width: none;
      .img-box {
        display: flex;
        width: 100%;
        .img {
          width: 250px;
          height: 140px;
          margin-right: 15px;
          object-fit: cover;
        }
      }
    }
  }
  .content::-webkit-scrollbar {
    display: none;
  }
  .m-w {
    min-width: fit-content;
    font-size: 16px;
    font-weight: bold;
  }
}
</style>
