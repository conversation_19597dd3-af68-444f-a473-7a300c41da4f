<script setup>
import { onMounted, ref } from "vue";
// import { formatTime } from "@/utils/index";
// import { commentsFindAll, upOnlyMe } from "@/api/course.js";
// import { requestTo } from "@/utils/http/tool";
// import { ElMessage, ElMessageBox } from "element-plus";
import { to } from "@iceywu/utils";
import { useUserStoreHook } from "@/store/modules/user";
// import { useRouter, useRoute } from "vue-router";
import {
  courseKnowledgePointFind,
  educationBureauCourseKnowledgePointFind
} from "@/api/course";
const props = defineProps({
  periodId: {
    type: Number,
    default: 0
  }
});
onMounted(() => {
  getTableList();
});
// 表格数据
const tableData = ref([]);
// 获取列表信息
const getListLoading = ref(false);
const getTableList = async data => {
  if (getListLoading.value) {
    return;
  }

  const params = {
    coursePeriodId: props.periodId
  };
  let api =
    useUserStoreHook().roleTarget === "局端管理员"
      ? educationBureauCourseKnowledgePointFind
      : courseKnowledgePointFind;
  let [err, res] = await to(api(params));

  if (res.code === 200) {
    tableData.value = res?.data;
  } else {
    console.log("🌵-----err-----", err);
  }
  getListLoading.value = false;
};
</script>

<template>
  <div class="containers">
    <div class="con_table">
      <el-table
        :data="tableData"
        table-layout="fixed"
        :header-cell-style="{ backgroundColor: '#fafafa', color: '#565353' }"
        highlight-current-row
        height="100%"
        :style="{ flex: 1, minHeight: 0 }"
      >
        <el-table-column
          prop="stage"
          label="学段"
          align="left"
          fixed
          width="100"
        >
          <template #default="scope">
            {{ scope.row.stage?.name || "--" }}
          </template>
        </el-table-column>

        <el-table-column width="100px" prop="subject" label="学科">
          <template #default="scope">
            {{ scope.row.subject?.name || "--" }}
          </template>
        </el-table-column>
        <el-table-column
          prop="switch"
          label="教材关联"
          align="left"
          width="300"
        >
          <template #default="scope">
            <span
              v-if="scope.row.textbook?.name && scope.row.textbook?.name"
              class="no-wrap-text"
              >{{
                `${scope.row.textbook?.name} > ${scope.row.catalog?.name}`
              }}</span>
            <span v-else class="no-wrap-text">--</span>
          </template>
        </el-table-column>
        <el-table-column width="200px" prop="knowledge" label="核心知识点">
          <template #default="scope">
            <div>
              {{ scope.row.knowledge?.name || "--" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="content"
          label="预期目标"
          align="left"
          width="400"
        >
          <template #default="scope">
            <!-- <span class="no-wrap-text">{{ (scope.row.content) || "--" }}</span> -->
            <div v-html="scope.row.content || '--'" />
          </template>
        </el-table-column>
        <el-table-column
          prop="abilities"
          label="能力提升"
          align="left"
          width="200"
        >
          <template #default="scope">
            <span v-if="scope.row.abilities.length > 0" class="no-wrap-text">{{
              scope.row.abilities.map(i => i.abilityDict.name).join("，")
            }}</span>
            <span v-else class="no-wrap-text">--</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.containers {
  height: 100%;
  display: flex;
  flex-direction: column;
  //   box-sizing: border-box;
  //   width: calc(100% - 48px);
  //   height: 100%;
  //   padding: 24px;
  //   background: #fff;

  .con_search {
    display: flex;
    align-items: center;
    width: 100%;
    height: fit-content;
    margin-bottom: 12px;
  }

  .con_table {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;
    // width: calc(100% - 25px);
    // margin-bottom: 24px;
    // margin-left: 25px;
    width: 100%;
    margin: 10px 0 12px 0;

    .option {
      display: flex;

      .btnse {
        display: flex;
        // margin-left: 16px;
        color: #409eff;
        cursor: pointer;
        white-space: nowrap;
        margin-right: 20px;

        .nofreeze {
          color: #f56c6c;
          cursor: pointer;
        }
      }
      .other {
        margin-left: 16px;
      }
    }
    /* 强制不换行，超出显示省略号 */
    .no-wrap-text {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: inline-block;
      width: 100%;
    }
  }
}
:deep(.el-popper.is-dark) {
  max-width: 500px !important;
  word-break: break-all !important;
}
</style>
