<script setup>
import { onMounted, ref } from "vue";
import { formatTime } from "@/utils/index";
// import { courseFindAll } from "@/api/brUser";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import {
  priceSettingFindAll,
  findFree,
  bureauPriceSettingFindAll,
  bureauFindfree
} from "@/api/course";
import { to } from "@iceywu/utils";
import { useUserStoreHook } from "@/store/modules/user";
const router = useRouter();
const route = useRoute();
const freeValue = ref("");
const refundValue = ref("");
onMounted(() => {
  getTableList();
  getFreeData();
  // tableData.value = Array.from({ length: 20 }).fill({
  //   completed_at: "0",
  //   pending_review: 1,
  //   createdAt: 172627952334,
  //   name: "2024-09-14习题任务",
  //   termNumber: 1,
  //   courseTypeName: "手工",
  //   state: 1,
  //   organizationName: "2024-09-14习题任务",
  //   freeze: 1
  // });
});
// 表格数据
const tableData = ref([]);
const tableTitle = ref([]);
const params = ref({
  page: 1,
  size: 10,
  sort: "createdAt,desc",
  totalElements: 0
});
// 获取规格列表信息
const getListLoading = ref(false);
const getTableList = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    coursePeriodId: route.query.periodId
  };
  // console.log("🍧-----paramsData-----", paramsData);
  const api =
    useUserStoreHook().roleTarget === "局端管理员"
      ? bureauPriceSettingFindAll
      : priceSettingFindAll;
  const [err, result] = await requestTo(api(paramsData));
  // console.log("🎁-----result--22---", result);
  if (result) {
    tableData.value = result?.content.map(item => {
      let obj = {};
      result?.headers.forEach((header, index) => {
        if (header === "价格") {
          obj[header] = item[index] + " 元";
        } else {
          obj[header] = item[index];
        }
      });
      return obj;
    });
    tableTitle.value = result?.headers.map(it => {
      return {
        label: it,
        prop: it
      };
    });
    // console.log(
    //   "🌳 tableData.value------------------------------>",
    //   tableData.value
    // );
  } else {
    console.log("没有数据");
  }
  getListLoading.value = false;
};
// 获取费用说明及退款信息
const getLoading = ref(false);
const getFreeData = async data => {
  if (getLoading.value) {
    return;
  }
  getLoading.value = true;
  let paramsData = {
    coursePeriodId: route.query.periodId
  };
  const api =
    useUserStoreHook().roleTarget === "局端管理员" ? bureauFindfree : findFree;
  const [err, result] = await to(api(paramsData));
  if (result?.code === 200) {
    freeValue.value = result?.data?.feeDescription;
    refundValue.value = result?.data?.refundPolicy;
  } else {
    // ElMessage.error('获取失败');
    console.log("无数据");
  }
  getLoading.value = false;
};
</script>

<template>
  <div class="containers">
    <div class="con_table">
      <div v-if="tableData?.length" class="table-content">
        <pure-table
          ref="tableRef"
          row-key="id"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          table-layout="auto"
          :loading="getListLoading"
          :size="size"
          :data="tableData"
          :columns="tableTitle"
          :style="{ height: '100%' }"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @drag-sort-end="handleSortEnd"
        />
      </div>
      <el-skeleton v-else-if="getListLoading" :rows="5" animated />
      <el-empty
        v-else
        description="暂无数据"
        class="w100%"
        :style="{ flex: 1 }"
      />
    </div>
    <div class="content">
      <div class="free">
        <div class="title">费用说明（包含、不包含）</div>
        <div class="text">
          <el-input
            v-model="freeValue"
            :rows="6"
            type="textarea"
            resize="none"
            disabled
            :placeholder="freeValue ? freeValue : '暂无数据'"
          />
        </div>
      </div>
      <div class="refund">
        <div class="title">退款政策</div>
        <div class="text">
          <el-input
            v-model="refundValue"
            :rows="6"
            type="textarea"
            resize="none"
            disabled
            :placeholder="refundValue ? refundValue : '暂无数据'"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.containers {
  height: 100%;
  display: flex;
  flex-direction: column;
  // min-height: 0;
  //   box-sizing: border-box;
  //   width: calc(100% - 48px);
  //   height: 100%;
  //   padding: 24px;
  //   background: #fff;

  .con_table {
    // width: calc(100% - 25px);
    // margin-bottom: 24px;
    // margin-left: 25px; margin-bottom: 15px;
    // margin-left: 25px;
    // display: flex;
    // height: 248px;
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;
    .table-content {
      width: 100%;
      display: flex;
      flex-direction: column;
      flex: 1;
      min-height: 0;
      :deep(.el-table) {
        flex: 1;
        height: 100%;
        width: 100%;
        // display: flex;
        // flex-direction: column;
        // :deep(.el-table__body-wrapper) {
        //   overflow-y: auto;
        //   flex-grow: 1;
        // }
      }
      :deep(.pure-table) {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
      }
    }
  }

  .content {
    box-sizing: border-box;
    display: flex;
    padding: 10px 0 10px 0;

    .free {
      width: 50%;
      margin-right: 40px;
    }

    .title {
      margin-bottom: 18px;
      font-size: 14px;
      color: rgb(16 16 16 / 100%);
    }

    .refund {
      width: 50%;
    }

    .text {
      width: 100%;
    }
  }
}
</style>
