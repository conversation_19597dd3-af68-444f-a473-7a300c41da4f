<script setup>
import { ref, onMounted } from "vue";
import { requestTo } from "@/utils/http/tool";
import { itineraryFindAll, bureauItineraryFindAll } from "@/api/course.js";
import dayjs from "dayjs";
import { useUserStoreHook } from "@/store/modules/user";
const props = defineProps({
  periodId: {
    type: Number,
    default: 0
  }
});
const tripList = ref([]);
// 课期行程列表查询
const findCoursePeriodList = async type => {
  const params = {
    coursePeriodId: props.periodId
  };
  const api =
    useUserStoreHook().roleTarget === "局端管理员"
      ? bureauItineraryFindAll
      : itineraryFindAll;
  const [err, res] = await requestTo(api(params));
  if (res) {
    // console.log("🌳-----res-----", res);
    tripList.value = res;
  } else {
    console.log("🌵-----err-----", err);
  }
};
onMounted(() => {
  //   console.log("🌈periodId------------------------------>", props.periodId);
  findCoursePeriodList();
});
</script>

<template>
  <div class="scheduling">
    <div v-if="tripList?.length" class="content">
      <el-timeline style="max-width: 600px">
        <el-timeline-item
          v-for="item in tripList"
          :key="item.id"
          :timestamp="item.timestamp"
        >
          <div class="timeLine_title">
            <span class="m-w">{{ item.title }}</span>
            <span class="m-w">{{
              `${dayjs(item.startTime).format("YYYY-MM-DD")} ` +
              `(${dayjs(item.startTime).format("dddd")})`
            }}</span>
            <span class="m-w">{{
              dayjs(item.startTime).format("HH:mm:ss")
            }}</span>
          </div>
          <div class="item_content">
            <div v-html="item.content" />
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>

    <el-empty v-else description="暂无数据" />
  </div>
</template>

<style lang="scss" scoped>
.scheduling {
  height: 100%;
  width: 100%;
  position: relative;

  .content {
    width: 100%;
    height: 97%;
    overflow-y: auto;
    padding: 10px;
    box-sizing: border-box;
    -ms-overflow-style: none;
    scrollbar-width: none;
    .timeLine_title {
      width: 100%;
      display: flex;
      align-items: center;
      gap: 20px;
    }
    .item_content {
      margin-top: 20px;
      width: 100%;
      padding-left: 30px;
    }
  }
  .content::-webkit-scrollbar {
    display: none;
  }
  .m-w {
    min-width: fit-content;
    font-size: 16px;
    font-weight: bold;
  }
}
</style>
