import {
  onMounted,
  ref,
  onActivated,
  nextTick,
  onUnmounted,
  onDeactivated,
  computed,
  h
} from "vue";
import { formatTime } from "@/utils/index";
import { requestTo } from "@/utils/http/tool";
import {
  ElMessage,
  ElMessageBox,
  ElImage,
  ElButton,
  ElDialog,
  ElTag
} from "element-plus";
import { useRouter } from "vue-router";

export const useAppealManagement = () => {
  const router = useRouter();

  // 申诉状态tab选项
  const activeTab = ref(0);
  const tabOptions = ref([
    { id: 0, name: "待处理", value: "PENDING" },
    { id: 1, name: "已处理", value: "PROCESSED" }
  ]);

  // 申诉数据对象
  const appealObj = ref({
    tabId: 0,
    state: "PENDING",
    showBatchAudit: true // 待处理显示批量审核，已处理不显示
  });

  // 搜索表单数据
  const searchForm = ref({
    content: "" // 内容搜索
  });

  // 表格数据
  const tableData = ref([]);
  const loading = ref(false);

  // 分页配置
  const pagination = ref({
    current: 1,
    pageSize: 10,
    total: 0
  });

  // 选中的行
  const selectedRows = ref([]);

  // 多选模式控制
  const isSelectionMode = ref(false);

  // 申诉内容详情弹窗
  const appealDetailVisible = ref(false);
  const currentAppealDetail = ref(null);

  // 搜索表单配置
  const searchColumns = ref([
    {
      label: "内容",
      prop: "content",
      valueType: "input",
      fieldProps: {
        placeholder: "请输入内容搜索",
        clearable: true
      }
    }
  ]);

  // Mock数据生成
  const generateMockData = status => {
    const mockData = [
      {
        id: 1,
        appealUser: "张三",
        appealContent: {
          type: "mixed", // 混合类型：文字+图片
          text: "课程内容与描述不符，申请退款。课程质量较差，讲师讲解不清晰，实际内容与宣传内容差距较大。以下是相关截图证明：",
          images: [
            "https://picsum.photos/300/200?random=1",
            "https://picsum.photos/300/200?random=2"
          ],
          videos: []
        },
        orderId: "ORD202501001",
        courseName: "Vue3高级开发实战",
        appealTime: "2025-01-15 14:30:25",
        status: status,
        studentId: "STU001",
        courseId: "COU001",
        refundAmount: 299.0
      },
      {
        id: 2,
        appealUser: "李四",
        appealContent: {
          type: "mixed", // 混合类型：文字+视频
          text: "讲师授课质量不佳，课程进度过快，无法跟上学习节奏。录制了课堂视频作为证据：",
          images: [],
          videos: [
            {
              url: "https://www.w3schools.com/html/mov_bbb.mp4",
              poster: "https://picsum.photos/300/200?random=3",
              title: "课堂录像1"
            }
          ]
        },
        orderId: "ORD202501002",
        courseName: "React企业级项目开发",
        appealTime: "2025-01-15 16:45:12",
        status: status,
        studentId: "STU002",
        courseId: "COU002",
        refundAmount: 399.0
      },
      {
        id: 3,
        appealUser: "王五",
        appealContent: {
          type: "mixed", // 混合类型：文字+图片+视频
          text: "课程视频无法正常播放，技术支持响应缓慢。附上错误截图和录屏视频：",
          images: ["https://picsum.photos/300/200?random=4"],
          videos: [
            {
              url: "https://www.w3schools.com/html/mov_bbb.mp4",
              poster: "https://picsum.photos/300/200?random=5",
              title: "播放错误录屏"
            }
          ]
        },
        orderId: "ORD202501003",
        courseName: "Node.js后端开发进阶",
        appealTime: "2025-01-14 09:20:33",
        status: status,
        studentId: "STU003",
        courseId: "COU003",
        refundAmount: 199.0
      },
      {
        id: 4,
        appealUser: "赵六",
        appealContent: {
          type: "text", // 纯文字类型
          text: "课程内容过于基础，与高级课程描述不符，申请退款并重新选择适合的课程。课程大纲与实际教学内容存在较大差异，希望能够得到合理的解决方案。",
          images: [],
          videos: []
        },
        orderId: "ORD202501004",
        courseName: "Python数据分析实战",
        appealTime: "2025-01-14 11:15:47",
        status: status,
        studentId: "STU004",
        courseId: "COU004",
        refundAmount: 599.0
      },
      {
        id: 5,
        appealUser: "孙七",
        appealContent: {
          type: "mixed", // 混合类型：文字+多张图片
          text: "讲师临时更换，新讲师教学风格不适应，课程质量下降。以下是课程质量对比截图：",
          images: [
            "https://picsum.photos/300/200?random=6",
            "https://picsum.photos/300/200?random=7",
            "https://picsum.photos/300/200?random=8"
          ],
          videos: []
        },
        orderId: "ORD202501005",
        courseName: "Java Spring Boot开发",
        appealTime: "2025-01-13 15:30:18",
        status: status,
        studentId: "STU005",
        courseId: "COU005",
        refundAmount: 799.0
      },
      {
        id: 6,
        appealUser: "周八",
        appealContent: {
          type: "images", // 纯图片类型
          text: "",
          images: [
            "https://picsum.photos/300/200?random=9",
            "https://picsum.photos/300/200?random=10"
          ],
          videos: []
        },
        orderId: "ORD202501006",
        courseName: "微信小程序开发实战",
        appealTime: "2025-01-13 10:20:15",
        status: status,
        studentId: "STU006",
        courseId: "COU006",
        refundAmount: 399.0
      }
    ];

    return mockData;
  };

  // 查看申诉详情
  const handleViewAppealDetail = appealContent => {
    currentAppealDetail.value = appealContent;
    appealDetailVisible.value = true;
  };

  // 关闭申诉详情弹窗
  const handleCloseAppealDetail = () => {
    appealDetailVisible.value = false;
    currentAppealDetail.value = null;
  };

  // 渲染申诉内容摘要
  const renderAppealContentSummary = appealContent => {
    if (!appealContent) return h("span", "暂无内容");

    const { type, text, images, videos } = appealContent;

    // 创建内容摘要
    const textSummary = text
      ? text.length > 50
        ? text.substring(0, 50) + "..."
        : text
      : "";
    const mediaInfo = [];

    if (images && images.length > 0) {
      mediaInfo.push(`${images.length}张图片`);
    }
    if (videos && videos.length > 0) {
      mediaInfo.push(`${videos.length}个视频`);
    }

    const elements = [];

    // 添加文字内容
    if (textSummary) {
      elements.push(h("div", { class: "appeal-text-summary" }, textSummary));
    }

    // 添加媒体标签
    if (mediaInfo.length > 0) {
      const tags = mediaInfo.map(info =>
        h(
          ElTag,
          {
            size: "small",
            type: "info",
            style: { marginRight: "4px", marginTop: "4px" }
          },
          () => info
        )
      );
      elements.push(h("div", { class: "appeal-media-tags" }, tags));
    }

    // 添加查看详情按钮
    elements.push(
      h(
        ElButton,
        {
          type: "text",
          size: "small",
          style: { marginTop: "4px" },
          onClick: () => handleViewAppealDetail(appealContent)
        },
        () => "查看详情"
      )
    );

    return h("div", { class: "appeal-content-summary" }, elements);
  };

  // 表格列配置
  const columns = computed(() => [
    {
      label: "申诉人",
      prop: "appealUser",
      width: 100,
      align: "center"
    },
    {
      label: "申诉内容",
      prop: "appealContent",
      minWidth: 300,
      render: value => renderAppealContentSummary(value)
    },
    {
      label: "申诉订单",
      prop: "orderId",
      width: 140,
      align: "center"
    },
    {
      label: "课程名称",
      prop: "courseName",
      width: 180,
      showOverflowTooltip: true
    },
    {
      label: "申诉时间",
      prop: "appealTime",
      width: 160,
      align: "center"
    }
  ]);

  // 加载表格数据
  const loadTableData = () => {
    loading.value = true;

    // 模拟API调用
    setTimeout(() => {
      const mockData = generateMockData(appealObj.value.state);

      // 根据搜索关键词过滤数据
      let filteredData = mockData;
      if (searchForm.value.content && searchForm.value.content.trim()) {
        filteredData = mockData.filter(item => {
          const searchTerm = searchForm.value.content.toLowerCase();
          const appealText = item.appealContent?.text || "";

          return (
            item.appealUser.toLowerCase().includes(searchTerm) ||
            appealText.toLowerCase().includes(searchTerm) ||
            item.courseName.toLowerCase().includes(searchTerm) ||
            item.orderId.toLowerCase().includes(searchTerm)
          );
        });
      }

      tableData.value = filteredData;
      pagination.value.total = filteredData.length;
      loading.value = false;
    }, 500);
  };

  // 处理申诉
  const handleProcess = row => {
    ElMessageBox.confirm(
      `确定要将申诉人"${row.appealUser}"的申诉标记为已处理吗？`,
      "确认处理",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    )
      .then(() => {
        // 模拟处理申诉
        ElMessage.success("申诉处理成功");
        loadTableData();
      })
      .catch(() => {
        // 取消处理
      });
  };

  // 操作栏配置
  const actionBar = computed(() => ({
    label: "处理状态", // 设置操作栏表头文字
    buttons: [
      {
        text: "已处理",
        type: "edit",
        props: () => ({
          type: "primary",
          disabled: appealObj.value.state === "PROCESSED" // 已处理状态下禁用
        }),
        show: true, // 两个状态都显示
        onClick: row => {
          if (appealObj.value.state === "PENDING") {
            handleProcess(row);
          }
          // 已处理状态下不执行任何操作
        }
      }
    ]
  }));

  // 切换tab
  const handleTabClick = (item, index) => {
    activeTab.value = item.props.name;

    if (activeTab.value === 0) {
      // 待处理
      appealObj.value.tabId = 0;
      appealObj.value.state = "PENDING";
      appealObj.value.showBatchAudit = true;
    } else {
      // 已处理
      appealObj.value.tabId = 1;
      appealObj.value.state = "PROCESSED";
      appealObj.value.showBatchAudit = false;
    }

    // 切换tab时关闭多选模式
    isSelectionMode.value = false;
    selectedRows.value = [];

    // 重新加载数据
    loadTableData();
  };

  // 搜索
  const handleSearch = () => {
    pagination.value.current = 1;
    loadTableData();
  };

  // 重置搜索
  const handleReset = () => {
    searchForm.value = {
      content: ""
    };
    pagination.value.current = 1;
    loadTableData();
  };

  // 开启批量处理模式
  const handleEnableBatchMode = () => {
    isSelectionMode.value = true;
    selectedRows.value = [];
  };

  // 取消批量处理模式
  const handleCancelBatchMode = () => {
    isSelectionMode.value = false;
    selectedRows.value = [];
  };

  // 执行批量处理
  const handleBatchProcess = () => {
    if (selectedRows.value.length === 0) {
      ElMessage.warning("请先选择要处理的申诉记录");
      return;
    }

    ElMessageBox.confirm(
      `确定要批量处理选中的 ${selectedRows.value.length} 条申诉记录吗？`,
      "批量处理确认",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    )
      .then(() => {
        // 模拟批量处理
        ElMessage.success(`成功处理 ${selectedRows.value.length} 条申诉记录`);
        selectedRows.value = [];
        isSelectionMode.value = false; // 处理完成后关闭多选模式
        loadTableData();
      })
      .catch(() => {
        // 取消处理
      });
  };

  // 表格选择变化
  const handleSelectionChange = selection => {
    selectedRows.value = selection;
  };

  // 分页变化
  const handlePageChange = page => {
    pagination.value.current = page;
    loadTableData();
  };

  const handleSizeChange = size => {
    pagination.value.pageSize = size;
    pagination.value.current = 1;
    loadTableData();
  };

  // 组件挂载时加载数据
  onMounted(() => {
    loadTableData();
  });

  // 返回所有需要在模板中使用的数据和方法
  return {
    // 响应式数据
    activeTab,
    tabOptions,
    appealObj,
    searchForm,
    tableData,
    loading,
    pagination,
    selectedRows,
    isSelectionMode,
    searchColumns,
    columns,
    actionBar,
    appealDetailVisible,
    currentAppealDetail,

    // 方法
    handleTabClick,
    loadTableData,
    handleSearch,
    handleReset,
    handleProcess,
    handleEnableBatchMode,
    handleCancelBatchMode,
    handleBatchProcess,
    handleSelectionChange,
    handlePageChange,
    handleSizeChange,
    handleViewAppealDetail,
    handleCloseAppealDetail
  };
};
