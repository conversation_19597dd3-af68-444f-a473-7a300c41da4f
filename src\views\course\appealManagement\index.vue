<script setup>
import { useAppealManagement } from "./components/appealManagementHook.jsx";

defineOptions({
  name: "AppealManagementIndex"
});

// 使用申诉管理 hook
const {
  // 响应式数据
  activeTab,
  tabOptions,
  appealObj,
  searchForm,
  tableData,
  loading,
  pagination,
  selectedRows,
  isSelectionMode,
  searchColumns,
  columns,
  actionBar,
  appealDetailVisible,
  currentAppealDetail,

  // 方法
  handleTabClick,
  loadTableData,
  handleSearch,
  handleReset,
  handleProcess,
  handleEnableBatchMode,
  handleCancelBatchMode,
  handleBatchProcess,
  handleSelectionChange,
  handlePageChange,
  handleSizeChange,
  handleViewAppealDetail,
  handleCloseAppealDetail
} = useAppealManagement();
</script>

<template>
  <div class="containers">
    <div class="main">
      <!-- 申诉管理tab栏 -->
      <div class="appeal-tabs">
        <el-tabs
          v-model="activeTab"
          class="demo-tabs"
          @tab-click="handleTabClick"
        >
          <el-tab-pane
            v-for="(item, index) in tabOptions"
            :key="index"
            :label="item.name"
            :name="index"
          />
        </el-tabs>
      </div>

      <!-- 搜索区域 -->
      <div class="search-section">
        <PlusSearch
          v-model="searchForm"
          :columns="searchColumns"
          :show-number="1"
          :has-unfold="false"
          @search="handleSearch"
          @reset="handleReset"
        />
      </div>

      <!-- 批量操作按钮 -->
      <div v-if="appealObj.showBatchAudit" class="batch-actions">
        <el-button
          type="primary"
          :disabled="isSelectionMode"
          @click="handleEnableBatchMode"
        >
          批量处理
        </el-button>
      </div>

      <!-- 申诉列表表格 -->
      <div class="appeal-content">
        <PlusTable
          :title-bar="false"
          :columns="columns"
          :table-data="tableData"
          :loadingStatus="loading"
          :action-bar="actionBar"
          :is-selection="isSelectionMode"
          :pagination="pagination"
          :adaptive="true"
          row-key="id"
          @selection-change="handleSelectionChange"
          @page-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>

      <!-- 多选模式下的操作按钮 -->
      <div v-if="isSelectionMode" class="selection-actions">
        <el-button
          type="primary"
          :disabled="selectedRows.length === 0"
          @click="handleBatchProcess"
        >
          处理
        </el-button>
        <el-button @click="handleCancelBatchMode"> 取消 </el-button>
      </div>
    </div>

    <!-- 申诉详情弹窗 -->
    <el-dialog
      v-model="appealDetailVisible"
      title="申诉详情"
      width="800px"
      @close="handleCloseAppealDetail"
    >
      <div v-if="currentAppealDetail" class="appeal-detail-content">
        <!-- 文字内容 -->
        <div v-if="currentAppealDetail.text" class="appeal-text">
          <h4>申诉描述：</h4>
          <p>{{ currentAppealDetail.text }}</p>
        </div>

        <!-- 图片内容 -->
        <div
          v-if="
            currentAppealDetail.images && currentAppealDetail.images.length > 0
          "
          class="appeal-images"
        >
          <h4>相关图片：</h4>
          <div class="image-gallery">
            <el-image
              v-for="(image, index) in currentAppealDetail.images"
              :key="index"
              :src="image"
              :preview-src-list="currentAppealDetail.images"
              :initial-index="index"
              fit="cover"
              class="appeal-image"
              lazy
            />
          </div>
        </div>

        <!-- 视频内容 -->
        <div
          v-if="
            currentAppealDetail.videos && currentAppealDetail.videos.length > 0
          "
          class="appeal-videos"
        >
          <h4>相关视频：</h4>
          <div class="video-gallery">
            <div
              v-for="(video, index) in currentAppealDetail.videos"
              :key="index"
              class="video-item"
            >
              <h5 v-if="video.title">{{ video.title }}</h5>
              <video
                :src="video.url"
                :poster="video.poster"
                controls
                preload="metadata"
                class="appeal-video"
              >
                您的浏览器不支持视频播放。
              </video>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.containers {
  display: flex;
  flex-direction: column;
  height: 88vh;
  overflow: hidden;
  box-sizing: border-box;

  .main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: #fff;
    padding: 20px;
    box-sizing: border-box;
  }
}

// 申诉管理tab样式
.appeal-tabs {
  margin-bottom: 20px;

  :deep(.el-tabs) {
    --el-tabs-header-height: 40px;
  }

  // 保持蓝色下划线效果
  :deep(.el-tabs__active-bar) {
    background-color: #409eff;
    height: 2px;
  }

  :deep(.el-tabs__nav-wrap::after) {
    background-color: #e4e7ed;
    height: 1px;
  }

  :deep(.el-tabs__item) {
    font-size: 14px;
    font-weight: 500;
    color: #606266;

    &.is-active {
      color: #409eff;
      font-weight: 600;
    }

    &:hover {
      color: #409eff;
    }
  }
}

// 搜索区域
.search-section {
  margin-bottom: 16px;

  :deep(.el-form-item__content .el-icon) {
    display: none;
  }
}

// 批量操作按钮区域
.batch-actions {
  margin-bottom: 16px;

  .el-button {
    margin-right: 12px;
  }
}

// 申诉内容区域
.appeal-content {
  flex: 1;
  overflow: hidden;

  :deep(.plus-table) {
    height: 100%;

    .el-table {
      height: calc(100% - 60px) !important;
    }
  }
}

// 申诉详情弹窗样式
.appeal-detail-content {
  .appeal-text {
    margin-bottom: 20px;

    h4 {
      margin: 0 0 10px 0;
      color: #303133;
      font-size: 14px;
      font-weight: 600;
    }

    p {
      margin: 0;
      line-height: 1.6;
      color: #606266;
      word-break: break-word;
    }
  }

  .appeal-images {
    margin-bottom: 20px;

    h4 {
      margin: 0 0 10px 0;
      color: #303133;
      font-size: 14px;
      font-weight: 600;
    }

    .image-gallery {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;

      .appeal-image {
        width: 120px;
        height: 120px;
        border-radius: 6px;
        cursor: pointer;
        border: 1px solid #dcdfe6;

        &:hover {
          border-color: #409eff;
        }
      }
    }
  }

  .appeal-videos {
    h4 {
      margin: 0 0 10px 0;
      color: #303133;
      font-size: 14px;
      font-weight: 600;
    }

    .video-gallery {
      .video-item {
        margin-bottom: 15px;

        &:last-child {
          margin-bottom: 0;
        }

        h5 {
          margin: 0 0 8px 0;
          color: #606266;
          font-size: 13px;
          font-weight: 500;
        }

        .appeal-video {
          width: 100%;
          max-width: 500px;
          height: auto;
          border-radius: 6px;
          border: 1px solid #dcdfe6;
        }
      }
    }
  }
}

// 表格中申诉内容摘要样式
:deep(.appeal-content-summary) {
  .appeal-text-summary {
    margin-bottom: 6px;
    line-height: 1.4;
    color: #606266;
    word-break: break-word;
  }

  .appeal-media-tags {
    margin-bottom: 6px;

    .el-tag {
      margin-right: 4px;
      margin-bottom: 2px;
    }
  }
}
</style>
