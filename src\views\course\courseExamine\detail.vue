<script setup>
import { ref, onMounted, nextTick } from "vue";
import { useRoute } from "vue-router";
import TabTitle from "@/components/Base/tabInfo.vue";
import BaseInfo from "./components/baseInfo.vue";
import Scheduling from "@/components/course/scheduling.vue";
import PriceSetting from "@/components/course/priceSetting.vue";
import courseIntroduction from "@/components/course/courseIntroduction.vue";
import JobDesign from "@/components/course/jobDesign.vue";
import Knowledge from "@/components/course/knowledge.vue";
import {
  findByApplyId,
  reviewApproval,
  findcoursePeriodId,
  findBasicInformation,
  bureauFindBasicInformation,
  toTheLocalEnd
} from "@/api/course.js";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { APPLY_STATE, APPROVAL_TYPE } from "@/utils/enum";
import { to } from "@iceywu/utils";
import { useUserStoreHook } from "@/store/modules/user";
import dayjs from "dayjs";
import { ImageThumbnail } from "@/utils/imageProxy.js";
import { localEndApplyReview, localEndFindByApplyId } from "@/api/localEnd.js";

const route = useRoute();
const textarea = ref("");
const infoShow = ref(true);
const tableShow = ref("基础信息");

// 费用相关字段
const serviceFeeRatio = ref("");
const serviceFee = ref("");
const insuranceFee = ref("");
const classHourFee = ref(0);

// 百分比转换辅助函数
const convertDecimalToPercentage = (decimal) => {
  if (decimal === null || decimal === undefined) return "";
  return decimal * 100;
};

const convertPercentageToDecimal = (percentage) => {
  if (!percentage || percentage === "") return null;
  return parseFloat(percentage) / 100;
};

// 失焦事件处理函数
const handleRatioBlur = () => {
  // 先处理小数位截断
  if (serviceFeeRatio.value !== '' && serviceFeeRatio.value !== null) {
    const num = parseFloat(serviceFeeRatio.value);
    if (!isNaN(num)) {
      const truncated = Math.floor(num * 100) / 100;
      const finalValue = truncated > 100 ? 100 : truncated;
      serviceFeeRatio.value = finalValue.toFixed(2).replace(/\.?0+$/, '');
    }
  }

  const classHour = parseFloat(classHourFee.value) || 0;
  if (classHour === 0) return; // 课时费为0时不联动

  const ratio = parseFloat(serviceFeeRatio.value);
  if (!isNaN(ratio) && ratio >= 0 && ratio <= 100) {
    const calculatedFee = (classHour * ratio / 100);
    const truncatedFee = Math.floor(calculatedFee * 100) / 100;
    serviceFee.value = truncatedFee.toFixed(2).replace(/\.?0+$/, '');
  }
};

const handleFeeBlur = () => {
  // 先处理小数位截断
  if (serviceFee.value !== '' && serviceFee.value !== null) {
    const num = parseFloat(serviceFee.value);
    if (!isNaN(num)) {
      const truncated = Math.floor(num * 100) / 100;
      serviceFee.value = truncated.toFixed(2).replace(/\.?0+$/, '');
    }
  }

  const classHour = parseFloat(classHourFee.value) || 0;
  if (classHour === 0) return; // 课时费为0时不联动

  const fee = parseFloat(serviceFee.value);
  if (!isNaN(fee) && fee >= 0) {
    const calculatedRatio = (fee / classHour) * 100;
    const truncatedRatio = Math.floor(calculatedRatio * 100) / 100;
    serviceFeeRatio.value = truncatedRatio.toFixed(2).replace(/\.?0+$/, '');
  }
};

// 保险费失焦处理
const handleInsuranceBlur = () => {
  if (insuranceFee.value !== '' && insuranceFee.value !== null) {
    const num = parseFloat(insuranceFee.value);
    if (!isNaN(num)) {
      const truncated = Math.floor(num * 100) / 100;
      insuranceFee.value = truncated.toFixed(2).replace(/\.?0+$/, '');
    }
  }
};

// 禁用负号输入
const handleKeydown = (event) => {
  // 禁用负号(-)和加号(+)
  if (event.key === '-' || event.key === '+') {
    event.preventDefault();
  }
};

// 输入验证函数
const validateNumberInput = (value, max = null) => {
  if (value === '' || value === null || value === undefined) return '';

  const num = parseFloat(value);
  if (isNaN(num)) return '';
  if (num < 0) return '';
  if (max !== null && num > max) return max;

  return value;
};

// 比例输入处理
const handleRatioInput = (value) => {
  serviceFeeRatio.value = validateNumberInput(value, 100);
};

// 服务费输入处理
const handleFeeInput = (value) => {
  serviceFee.value = validateNumberInput(value);
};

// 保险费输入处理
const handleInsuranceInput = (value) => {
  insuranceFee.value = validateNumberInput(value);
};

// 驳回理由相关
const rejectReasons = ref([
  "课程信息不完整",
  "课程内容质量问题",
  "课程安排不合理",
  "资质证明不齐全",
  "课程涉嫌侵权或违规",
  "课程定价不合理",
  "课程存在安全隐患",
  "营销宣传过度"
]);
const selectedReasons = ref([]);
const customReason = ref("");
const rejectDialogVisible = ref(false);

// 表头
const tableHeader = ref([
  {
    id: "1",
    label: "课程名",
    value: "--",
    width: "107px"
  },
  {
    id: "2",
    label: "期数",
    value: "--",
    width: "107px"
  },
  {
    id: "3",
    label: "机构",
    value: "--",
    width: "107px"
  },
  {
    id: "4",
    label: "申请时间",
    value: "--",
    width: "107px"
  },
  {
    id: "5",
    label: "审核状态",
    value: "--",
    width: "107px"
  },
  {
    id: "6",
    label: "审核类型",
    value: "--",
    width: "107px"
  }
]);
const tableHeaderDetail = ref([
  {
    id: "1",
    label: "课程名",
    value: "--",
    width: "107px"
  },
  {
    id: "2",
    label: "期数",
    value: "--",
    width: "107px"
  },
  {
    id: "3",
    label: "机构",
    value: "--",
    width: "107px"
  },
  {
    id: "4",
    label: "申请时间",
    value: "--",
    width: "107px"
  },
  {
    id: "5",
    label: "审核状态",
    value: "--",
    width: "107px"
  },
  {
    id: "6",
    label: "审核类型",
    value: "--",
    width: "107px"
  },
  {
    id: "7",
    label: "审核员",
    value: "--",
    width: "107px"
  },
  {
    id: "8",
    label: "课程分类",
    value: "--",
    width: "107px"
  },
  {
    id: "9",
    label: "审核意见",
    value: "--",
    width: "107px"
  }
]);
const tabTitle = ref([
  { id: 1, name: "基础信息" },
  { id: 2, name: "行程安排" },
  { id: 3, name: "课期介绍" },
  { id: 4, name: "课期知识点" },
  { id: 5, name: "材料说明" },
  { id: 6, name: "注意事项" },
  { id: 7, name: "价格设置" },
  { id: 8, name: "实践感悟" },
  { id: 9, name: "用户协议" }
]);
const baseInfo = ref([
  {
    id: "1",
    label: "课期ID",
    value: "--",
    width: "107px"
  },
  {
    id: "2",
    label: "创建时间",
    value: "--",
    width: "107px"
  },
  {
    id: "3",
    label: "人数上限",
    value: "--",
    width: "107px"
  },
  {
    id: "4",
    label: "开课时间",
    value: "--",
    width: "107px"
  },
  {
    id: "5",
    label: "实践点",
    value: "--",
    width: "107px"
  },

  {
    id: "6",
    label: "领队",
    value: "--",
    width: "107px"
  },
  {
    id: "7",
    label: "讲师",
    value: "--",
    width: "107px"
  }
  // {
  //   id: "8",
  //   label: "讲师",
  //   value: "--",
  //   width: "107px"
  // }

  // 其他表头数据项
]);
const noLoading = ref(false);
const passLoading = ref(false);
const tabInfoEvt = obj => {
  // console.log("💗tabInfoEvt---------->", obj);
  tableShow.value = obj.name;
};
// 不通过
const nopassEvt = text => {
  rejectDialogVisible.value = true;
};

// 通过
const showLocalEnd = ref(useUserStoreHook().roleTarget === "局端管理员");
const showLocalEndDialod = ref(false);
const passEvt = text => {
  passLoading.value = true;
  showLocalEndDialod.value = true;
  passLoading.value = false;
};
// 转交到局端
const getLocalEndPass = async () => {
  const operateLog = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: `转交了"${tableHeader.value[0].value}"课期的${tableHeader.value[5].value}申请到局端`
  };
  try {
    const data = await toTheLocalEnd(
      { id: Number(route.query.id) },
      operateLog
    );
    if (data.code === 200) {
      ElMessage.success("已转交局端审核");
      infoShow.value = !infoShow.value;
      getFindByApplyId();
    }
  } catch (err) {
    ElMessage.error("转交局端审核失败，请稍后重试");
  }
  showLocalEndDialod.value = false;
};

// 确认通过
const getPassEvt = async text => {
  try {
    getReviewApproval(text);
  } catch (error) {
    ElMessage.error("审核失败，请稍后重试");
    return;
  }
  showLocalEndDialod.value = false;
};

// 处理驳回理由选择
const handleReasonSelect = (reason, event) => {
  // 阻止事件冒泡，防止输入框失焦
  event.preventDefault();
  event.stopPropagation();

  const index = selectedReasons.value.indexOf(reason);
  if (index > -1) {
    // 取消选择
    selectedReasons.value.splice(index, 1);
    // 从输入框中移除该理由
    const reasonText = reason + "，";
    const regex = new RegExp(
      reasonText.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"),
      "g"
    );
    customReason.value = customReason.value.replace(regex, "").trim();
  } else {
    // 选择理由
    selectedReasons.value.push(reason);
    // 添加到输入框，后面加上中文逗号
    if (customReason.value) {
      customReason.value += reason + "，";
    } else {
      customReason.value = reason + "，";
    }
  }

  // 确保输入框保持焦点
  nextTick(() => {
    const textarea = document.querySelector(".custom-reason textarea");
    if (textarea) {
      textarea.focus();
    }
  });
};

// 处理自定义输入变化
const handleCustomReasonChange = value => {
  customReason.value = value;
  // 检查哪些预设理由在输入框中（包含逗号）
  selectedReasons.value = rejectReasons.value.filter(reason =>
    value.includes(reason + "，")
  );
};

// 关闭驳回弹窗
const handleRejectDialogClose = () => {
  rejectDialogVisible.value = false;
  // 清空驳回理由
  selectedReasons.value = [];
  customReason.value = "";
};

// 确认驳回
const confirmReject = () => {
  // 检查驳回理由是否为空
  if (!customReason.value.trim()) {
    ElMessage.warning("请填写驳回理由");
    return;
  }

  noLoading.value = true;
  getReviewApproval("REJECTED");
  rejectDialogVisible.value = false;
};

// 审核
const getReviewApproval = async text => {
  const paramsArg = {
    applyId: Number(route.query.id),
    auditState: text,
    userType:
      useUserStoreHook().roleTarget === "局端管理员"
        ? "EDUCATION_BUREAU"
        : "PLATFORM_ADMIN"
  };

  // 处理驳回理由，使用opinion参数
  if (text === "REJECTED") {
    const finalReason = customReason.value.trim();
    if (finalReason) {
      paramsArg.opinion = finalReason;
    }
  } else if (textarea.value) {
    paramsArg.opinion = textarea.value;
  }

  // 添加费用相关参数
  if (text === "APPROVED") {
    // serviceFeeRatio需要转换为小数
    if (serviceFeeRatio.value !== "" && serviceFeeRatio.value !== null && !isNaN(parseFloat(serviceFeeRatio.value))) {
      paramsArg.serviceFeeRatio = parseFloat(serviceFeeRatio.value) / 100;
    }
    if (serviceFee.value !== "" && serviceFee.value !== null && !isNaN(parseFloat(serviceFee.value))) {
      paramsArg.serviceFee = parseFloat(serviceFee.value);
    }
    if (insuranceFee.value !== "" && insuranceFee.value !== null && !isNaN(parseFloat(insuranceFee.value))) {
      paramsArg.insuranceFee = parseFloat(insuranceFee.value);
    }
  }

  const operateLog = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType:
      text === "REJECTED"
        ? `驳回了"${tableHeader.value[0].value}"课期的${tableHeader.value[5].value}申请`
        : `通过了"${tableHeader.value[0].value}"课期的${tableHeader.value[5].value}申请`
  };

  let api =
    useUserStoreHook().roleTarget === "局端管理员"
      ? localEndApplyReview
      : reviewApproval;
  const [err, res] = await to(api(paramsArg, operateLog));
  if (res.code === 200) {
    ElMessage.success("审核成功");
    infoShow.value = !infoShow.value;
    getFindByApplyId();
    // 清空驳回理由
    selectedReasons.value = [];
    customReason.value = "";
    // 清空费用字段
    classHourFee.value = 0;
    serviceFeeRatio.value = "";
    serviceFee.value = "";
    insuranceFee.value = "";
  } else {
    ElMessage.error(`审核失败,${res.msg}`);
  }
  if (err) {
    ElMessage.error("审核失败");
  }
  noLoading.value = false;
};
const params = ref({
  page: 1,
  size: 10,
  sort: "createdAt,desc",
  totalElements: 0
});
// 查询详情
let tableImg = ref("");
const getFindByApplyId = async () => {
  const paramsArg = {
    applyId: route.query.id
  };
  const api =
    useUserStoreHook().roleTarget === "局端管理员"
      ? localEndFindByApplyId
      : findByApplyId;
  const [err, res] = await requestTo(api(paramsArg));
  // console.log("🦄-----err, res-----", err, res);
  if (res) {
    // console.log("🐬res------------------------------>", res);
    if (
      res.auditState === "PENDING_REVIEW" ||
      (res.auditState === "EDUCATION_BUREAU_PENDING_REVIEW" &&
        showLocalEnd.value)
    ) {
      infoShow.value = false;
      tableHeader.value[0].value = res.coursePeriodName || "--";
      tableHeader.value[1].value = res.termNumber || "--";
      tableHeader.value[2].value = res.organizationName || "--";
      tableHeader.value[3].value = res.createdAt
        ? dayjs(res.createdAt).format("YYYY-MM-DD HH:mm:ss")
        : "--";
      tableHeader.value[4].value = APPLY_STATE[res.auditState]?.text || "--";
      tableHeader.value[5].value = APPROVAL_TYPE[res.applyType]?.text || "--";

      // 回显费用字段
      classHourFee.value = res.classHourFee !== null && res.classHourFee !== undefined ? res.classHourFee : 0;
      serviceFeeRatio.value = res.serviceFeeRatio !== null && res.serviceFeeRatio !== undefined ? (res.serviceFeeRatio * 100).toString() : "";
      serviceFee.value = res.serviceFee !== null && res.serviceFee !== undefined ? res.serviceFee.toString() : "";
      insuranceFee.value = res.insuranceFee !== null && res.insuranceFee !== undefined ? res.insuranceFee.toString() : "";
    } else {
      infoShow.value = true;
      tableHeaderDetail.value[0].value = res.coursePeriodName || "--";
      tableHeaderDetail.value[1].value = res.termNumber || "--";
      tableHeaderDetail.value[2].value = res.organizationName || "--";
      tableHeaderDetail.value[3].value = res.createdAt
        ? dayjs(res.createdAt).format("YYYY-MM-DD HH:mm:ss")
        : "--";
      tableHeaderDetail.value[4].value =
        APPLY_STATE[res.auditState]?.text || "--";
      tableHeaderDetail.value[5].value =
        APPROVAL_TYPE[res.applyType]?.text || "--";
      tableHeaderDetail.value[6].value = res.auditorName || "--";
      tableImg.value = res.files ? res.files[0]?.uploadFile?.url : "";
      tableHeaderDetail.value[7].value = res?.courseType || "--";
      tableHeaderDetail.value[8].value = res.opinion || "--";
    }

    // console.log('🌳 tableData.value------------------------------>',tableData.value);
  }
  if (err) {
    console.log("err------------------------------>", err);
  }
};
// 查询基础信息
const getCoursePeriodId = async () => {
  const paramsArg = {
    coursePeriodId: route.query.periodId
  };
  let api =
    useUserStoreHook().roleTarget === "局端管理员"
      ? bureauFindBasicInformation
      : findBasicInformation;
  const [err, res] = await requestTo(api(paramsArg));
  // console.log("🌈-----err, res-----", err, res);
  if (res) {
    // console.log("🐬res---------------3333---3333222------------>", res);
    baseInfo.value[0].value = res?.id || "--";
    baseInfo.value[1].value = res.createdAt
      ? dayjs(res.createdAt).format("YYYY-MM-DD HH:mm:ss")
      : "--";
    baseInfo.value[2].value = res?.maxPeopleNumber || "--";
    baseInfo.value[3].value = res.openTime
      ? dayjs(res.openTime).format("YYYY-MM-DD HH:mm:ss")
      : "--";
    baseInfo.value[4].value = res?.complex?.name || 0;
    baseInfo.value[5].value =
      res?.leaders?.map(it => it.name).join("、") || "--";
    baseInfo.value[6].value =
      res?.lecturers?.map(it => it.name).join("、") || "--";

    // console.log('🌳 tableData.value------------------------------>',tableData.value);
  }
  if (err) {
    console.log("err------------------------------>", err);
  }
};
onMounted(() => {
  getFindByApplyId();
  getCoursePeriodId();
});
</script>

<template>
  <div class="examine-detail">
    <div class="curse-table">
      <!-- 详情 -->
      <div v-if="infoShow" class="examine-info">
        <el-descriptions class="margin-top" title="" :column="3" border>
          <el-descriptions-item
            :rowspan="4"
            :width="140"
            label="课程封面"
            align="center"
          >
            <el-image v-if="tableImg" :src="ImageThumbnail(tableImg)" />
            <span v-else>暂无封面</span>
          </el-descriptions-item>
          <template v-for="(item, index) in tableHeaderDetail" :key="index">
            <el-descriptions-item
              v-if="
                !(
                  item.label === '审核意见' &&
                  tableHeaderDetail[4].value === '已通过'
                )
              "
              width="120px"
              label-align="center"
              :span="item.label === '课程分类' ? 3 : ''"
            >
              <template #label>
                <div class="cell-item">{{ item.label }}</div>
              </template>
              <div
                :style="{
                  color:
                    item.value === '已驳回'
                      ? '#FF374C'
                      : item.value === '已通过'
                        ? '#409EFF'
                        : item.value === '局端待审批'
                          ? '#FF9C41'
                          : ''
                }"
              >
                {{ item.value }}
              </div>
            </el-descriptions-item>
          </template>
        </el-descriptions>
      </div>
      <!-- 审核中 -->
      <div v-else class="examine-pending">
        <div class="pending-container">
          <div class="pengding-info">
            <el-descriptions class="margin-top" title="" :column="3" border>
              <template v-for="(item, index) in tableHeader" :key="index">
                <el-descriptions-item width="120px" label-align="center">
                  <template #label>
                    <div class="cell-item">{{ item.label }}</div>
                  </template>
                  <div
                    :style="{
                      color:
                        item.value === '待审批'
                          ? '#FF9C41'
                          : item.value === '局端待审批'
                            ? '#FF9C41'
                            : ''
                    }"
                  >
                    {{ item.value }}
                  </div>
                </el-descriptions-item>
              </template>
            </el-descriptions>

            <!-- 费用设置 -->
            <div class="fee-settings">
              <div class="fee-row">
                <div class="fee-item">
                  <label class="fee-label">平台服务费比例</label>
                  <el-input
                    v-model="serviceFeeRatio"
                    type="number"
                    :min="0"
                    :max="100"
                    step="0.01"
                    placeholder="5"
                    class="fee-input"
                    @blur="handleRatioBlur"
                    @keydown="handleKeydown"
                    @input="handleRatioInput"
                  />
                  <span class="fee-unit">%</span>
                </div>
                <div class="fee-item">
                  <label class="fee-label">平台服务费用</label>
                  <el-input
                    v-model="serviceFee"
                    type="number"
                    :min="0"
                    step="0.01"
                    placeholder="5"
                    class="fee-input"
                    @blur="handleFeeBlur"
                    @keydown="handleKeydown"
                    @input="handleFeeInput"
                  />
                  <span class="fee-unit">元</span>
                </div>
                <div class="fee-item">
                  <label class="fee-label">保险费用</label>
                  <el-input
                    v-model="insuranceFee"
                    type="number"
                    :min="0"
                    step="0.01"
                    placeholder="请输入"
                    class="fee-input"
                    @keydown="handleKeydown"
                    @input="handleInsuranceInput"
                    @blur="handleInsuranceBlur"
                  />
                  <span class="fee-unit">元</span>
                </div>
              </div>
            </div>
          </div>
          <div class="opinion-btn">
            <el-button
              type="primary"
              style="margin-bottom: 10px"
              :loading="passLoading"
              @click="passEvt('APPROVED')"
            >
              通过
            </el-button>
            <el-button
              type="danger"
              :loading="noLoading"
              @click="nopassEvt('REJECTED')"
            >
              驳回
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 驳回理由弹窗 -->
    <el-dialog
      v-model="rejectDialogVisible"
      title="驳回理由"
      width="600px"
      :before-close="handleRejectDialogClose"
    >
      <div class="reject-reason-container">
        <div class="reason-tags">
          <div class="reason-tags-title">选择驳回理由：</div>
          <div class="reason-tags-content">
            <el-tag
              v-for="reason in rejectReasons"
              :key="reason"
              :type="selectedReasons.includes(reason) ? 'primary' : 'info'"
              class="reason-tag"
              style="cursor: pointer; margin: 5px"
              @click="event => handleReasonSelect(reason, event)"
            >
              {{ reason }}
            </el-tag>
          </div>
        </div>
        <div class="custom-reason">
          <div class="custom-reason-title">
            <span style="color: #f56c6c; margin-left: 4px">*</span>
            驳回理由：
          </div>
          <el-input
            v-model="customReason"
            type="textarea"
            maxlength="200"
            show-word-limit
            :rows="4"
            placeholder="请输入或选择驳回理由（必填）"
            @input="handleCustomReasonChange"
          />
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleRejectDialogClose">取消</el-button>
          <el-button type="danger" @click="confirmReject">确定驳回</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 通过申请弹窗 -->
    <el-dialog v-model="showLocalEndDialod" title="确定通过" width="400">
      <div>确定要通过该申请吗？</div>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            v-if="!showLocalEnd"
            type="primary"
            plain
            @click="getLocalEndPass"
          >
            转交局端审核
          </el-button>
          <el-button v-else @click="showLocalEndDialod = false">
            取消
          </el-button>
          <el-button type="primary" @click="getPassEvt('APPROVED')">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <div class="info-table">
      <!-- tab切换 -->
      <TabTitle :tabTitle="tabTitle" @tab-data="tabInfoEvt" />
      <!-- 切换信息 -->
      <div class="tab-info">
        <BaseInfo
          v-if="tableShow === '基础信息'"
          :baseInfo="baseInfo"
          :baseInfoNo="baseInfoNo"
          :type="route.query.type"
        />
        <Scheduling
          v-if="tableShow === '行程安排'"
          :periodId="Number(route.query.periodId)"
        />
        <PriceSetting
          v-if="tableShow === '价格设置'"
          :periodId="Number(route.query.periodId)"
        />
        <courseIntroduction
          v-if="
            tableShow === '课期介绍' ||
            tableShow === '材料说明' ||
            tableShow === '注意事项' ||
            tableShow === '用户协议'
          "
          :periodId="Number(route.query.periodId)"
          :tableTitle="tableShow"
        />
        <Knowledge
          v-else-if="tableShow === '课期知识点'"
          :periodId="Number(route.query.periodId)"
        />
        <JobDesign
          v-if="tableShow === '实践感悟'"
          :periodId="Number(route.query.periodId)"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.examine-detail {
  height: 88vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .curse-table {
    box-sizing: border-box;
    width: 100%;
    padding: 24px 20px;
    margin-bottom: 30px;
    background-color: #fff;
    flex-shrink: 0; // 防止压缩

    .examine-pending {
      .pending-container {
        display: flex;
        height: 100%;
        align-items: flex-end; // 垂直底部对齐
        justify-content: flex-end; // 水平右侧对齐
        gap: 20px;

        .pengding-info {
          flex: 1;
        }

        .opinion-btn {
          display: flex;
          flex-direction: column;
          align-items: flex-end; // 按钮在容器中右对齐
          min-width: 80px;
        }
      }
    }

    // 费用设置样式
    .fee-settings {
      margin-top: 20px;
      padding: 16px;
      background-color: #f8f9fa;
      border-radius: 4px;
      border: 1px solid #e9ecef;

      .fee-row {
        display: flex;
        gap: 20px;
        align-items: center;

        .fee-item {
          display: flex;
          align-items: center;
          gap: 8px;

          .fee-label {
            font-size: 14px;
            color: #333;
            white-space: nowrap;
            min-width: 100px;
          }

          .fee-input {
            width: 80px;
          }

          .fee-unit {
            font-size: 14px;
            color: #666;
            min-width: 20px;
          }
        }
      }
    }
  }

  .info-table {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .tab-info {
      flex: 1;
      box-sizing: border-box;
      width: 100%;
      padding: 20px 20px;
      background-color: #fff;
      overflow-y: auto; // 内容过多时可滚动
    }
  }
}

// 驳回理由弹窗样式
.reject-reason-container {
  .reason-tags {
    margin-bottom: 20px;

    .reason-tags-title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 10px;
      color: #333;
    }

    .reason-tags-content {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    .reason-tag {
      transition: all 0.3s;

      &:hover {
        transform: scale(1.05);
      }
    }
  }

  .custom-reason {
    .custom-reason-title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 10px;
      color: #333;
    }
  }
}

:deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: #e1f5ff;
}
</style>
