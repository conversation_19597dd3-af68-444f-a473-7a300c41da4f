<script setup>
import { ref, onMounted, onActivated, onDeactivated } from "vue";
import { findReviewAll } from "@/api/course";
import { requestTo } from "@/utils/http/tool";
import dayjs from "dayjs";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
import { AUDITSTATE } from "@/utils/enum.js";
import { useAuthStore } from "@/store/modules/auth";
import { useUserStoreHook } from "@/store/modules/user";
import { localEndFindReview } from "@/api/localEnd.js";
defineOptions({
  name: "CourseExamineIndex"
});

const store = useAuthStore();
const router = useRouter();

// onActivated(() => {
//   getFindReviewAll();
// });

// 搜索表单
const form = ref({
  startTime: "", //时间
  endTime: "",
  time: "",
  organizationName: "", //机构
  termNumber: "", //期数
  coursePeriodName: "", //课程名
  coursePeriodState: "",
  auditState: ""
});
// 表格数据
const tableData = ref([]);

const params = ref({
  page: 1,
  size: 15,
  sort: "createdAt,desc",
  totalElements: 0
});

//分页
const handleCurrentChange = e => {
  params.value.page = e;
  getFindReviewAll();
};
const handleSizeChange = val => {
  params.value.page = 1;
  params.value.size = val;
  getFindReviewAll();
};
// 选择时间
const timeChange = value => {
  // console.log("🐬-----value-----", value);
  if (value) {
    form.value.startTime = new Date(value[0])?.getTime();
    const endDate = new Date(value[1]);
    endDate.setHours(23, 59, 59, 999); // 关键修改
    form.value.endTime = endDate.getTime();
  }
};
// 查询
const getFindReviewAll = async () => {
  const paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    auditorType: ""
  };
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (form.value[paramsDataKey]) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    }
  }
  if (useUserStoreHook().roleTarget === "局端管理员") {
    paramsData.auditorType = "EDUCATION_BUREAU";
  } else {
    delete paramsData.auditorType;
  }
  const api =
    useUserStoreHook().roleTarget === "局端管理员"
      ? localEndFindReview
      : findReviewAll;
  const [err, res] = await requestTo(api(paramsData));
  if (res) {
    // console.log("🐬res------------------------------>", res);
    params.value.totalElements = res.totalElements;
    tableData.value = res.content;
    // console.log('🌳 tableData.value------------------------------>',tableData.value);
  }
  if (err) {
    console.log("err------------------------------>", err);
  }
};
const value1 = ref([]);
// 清除数据
const clearEvt = val => {
  if (val === "time") {
    form.value.startTime = "";
    form.value.endTime = "";
  } else if (val === "organizationName") {
    form.value.organizationName = "";
  } else if (val === "coursePeriodName") {
    form.value.coursePeriodName = "";
  } else if (val === "termNumber") {
    form.value.termNumber = "";
  }
  // params.value.page = 1;
  getFindReviewAll();
};
// 重置
const setData = () => {
  form.value = {};
  params.value.page = 1;
  value1.value = [];
  getFindReviewAll();
};
//搜索
const searchData = () => {
  params.value.page = 1;
  getFindReviewAll();
};
// 详情
const detailEvt = (row, text) => {
  router.push({
    path: "/course/examine/detail",
    query: { id: row.id, type: text, periodId: row.coursePeriodId }
  });
};
const showLocalEnd = ref(useUserStoreHook().roleTarget === "局端管理员");
const options = [
  {
    value: "",
    label: "全部"
  },
  {
    value: "COURSE_PERIOD_ONLINE",
    label: "上架"
  },
  {
    value: "COURSE_PERIOD_OFFLINE",
    label: "下架"
  }
];
const stateOptions = [
  {
    value: "",
    label: "全部"
  },
  {
    value: "PENDING_REVIEW",
    label: "待审核"
  },
  {
    value: "REJECTED",
    label: "已驳回"
  },
  {
    value: "APPROVED",
    label: "审核通过"
  },
  {
    value: "EDUCATION_BUREAU_PENDING_REVIEW",
    label: "局端待审核"
  }
];

onMounted(() => {
  // getPlatformFindAll();
  getFindReviewAll();
  // tableData.value = Array.from({ length: 20 }).fill({
  //   completed_at: "0",
  //   pending_review: 1,
  //   createdAt: 172627952334,
  //   cursename: "2024-09-14习题任务",
  //   type: 1,
  //   resource_url: "",
  //   state: 1,
  //   organization: "2024-09-14习题任务"
  // });
});
</script>

<template>
  <div class="containers">
    <div class="con_top">
      <!-- <div class="titles">课程审核</div> -->
    </div>
    <div class="search">
      <div class="con_search">
        <el-form :model="form" :inline="true">
          <el-form-item label="申请时间">
            <el-date-picker
              v-model="value1"
              type="daterange"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              @change="timeChange"
              @clear="clearEvt('time')"
            />
          </el-form-item>

          <el-form-item label="机构">
            <el-input
              v-model.trim="form.organizationName"
              placeholder="请输入"
              style="width: 180px"
              clearable
              @clear="clearEvt('organizationName')"
            />
          </el-form-item>
          <el-form-item label="课程名">
            <el-input
              v-model.trim="form.coursePeriodName"
              placeholder="请输入"
              clearable
              style="width: 180px"
              @clear="clearEvt('coursePeriodName')"
            />
          </el-form-item>
          <el-form-item label="期数">
            <el-input
              v-model.trim="form.termNumber"
              placeholder="请输入"
              style="width: 180px"
              clearable
              @clear="clearEvt('termNumber')"
            />
          </el-form-item>
          <el-form-item label="审批类型">
            <el-select
              v-model="form.coursePeriodState"
              style="width: 120px"
              placeholder="请选择"
              :empty-values="[null, undefined]"
              :value-on-clear="null"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="审批状态">
            <el-select
              v-model="form.auditState"
              style="width: 120px"
              placeholder="请选择"
              :empty-values="[null, undefined]"
              :value-on-clear="null"
            >
              <el-option
                v-for="item in stateOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label=" ">
            <div class="flex">
              <el-button type="primary" @click="searchData">搜索</el-button>
              <el-button @click="setData">重置</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <div class="main">
      <el-scrollbar class="scrollbar">
        <div class="con_table">
          <el-table
            :data="tableData"
            table-layout="fixed"
            :header-cell-style="{
              backgroundColor: '#fafafa',
              color: '#565353'
            }"
            max-height="calc(100vh - 346px)"
          >
            <el-table-column
              prop="coursePeriodName"
              label="课期名"
              width="300"
              align="left"
              show-overflow-tooltip
              fixed
            >
              <template #default="scope">
                {{ scope.row.coursePeriodName || "--" }}
              </template>
            </el-table-column>
            <el-table-column prop="termNumber" label="期号" align="left">
              <template #default="scope">
                <div>
                  {{ scope.row.termNumber || "--" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="organizationName"
              label="机构"
              align="left"
              width="300"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ scope.row.organizationName || "--" }}
              </template>
            </el-table-column>
            <el-table-column min-width="200" prop="createdAt" label="创建时间">
              <template #default="{ row }">
                <div>
                  {{
                    row.createdAt
                      ? dayjs(row.createdAt).format("YYYY-MM-DD HH:mm:ss")
                      : "--"
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="applyType" label="审批类型" align="left">
              <template #default="{ row }">
                <div>
                  {{
                    row?.applyType === "COURSE_PERIOD_ONLINE"
                      ? "上架"
                      : row?.applyType === "COURSE_PERIOD_OFFLINE"
                        ? "下架"
                        : "--"
                  }}
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="auditState" label="审批状态" align="left">
              <template #default="{ row }">
                <div :style="{ color: AUDITSTATE[row?.auditState].color }">
                  {{
                    row?.auditState === "PENDING_REVIEW"
                      ? "待审核"
                      : row?.auditState === "REJECTED"
                        ? "已驳回"
                        : row?.auditState === "SYSTEM_REJECT"
                          ? "已驳回"
                          : row?.auditState === "APPROVED"
                            ? "审核通过"
                            : row?.auditState ===
                                "EDUCATION_BUREAU_PENDING_REVIEW"
                              ? "局端待审核"
                              : "--"
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="address"
              fixed="right"
              label="操作"
              align="left"
              width="90px"
            >
              <template #default="{ row }">
                <div class="option">
                  <div
                    v-if="
                      !(row.auditState === 'PENDING_REVIEW') && !showLocalEnd
                    "
                    v-code="['607']"
                    class="btnse"
                    @click="detailEvt(row, 'detail')"
                  >
                    详情
                  </div>
                  <div
                    v-else-if="
                      row.auditState === 'PENDING_REVIEW' ||
                      row.auditState === 'EDUCATION_BUREAU_PENDING_REVIEW'
                    "
                    class="btnse"
                    style="color: #3ad852"
                    @click="detailEvt(row, 'approve')"
                  >
                    审核
                  </div>
                  <div
                    v-if="
                      !(row.auditState === 'PENDING_REVIEW') &&
                      showLocalEnd &&
                      row.auditState !== 'EDUCATION_BUREAU_PENDING_REVIEW'
                    "
                    v-code="['607']"
                    class="btnse"
                    @click="detailEvt(row, 'detail')"
                  >
                    详情
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-scrollbar>

      <div class="con_pagination">
        <!-- 分页 -->
        <el-pagination
          v-model:current-page="params.page"
          v-model:page-size="params.size"
          class="pagination"
          background
          :page-sizes="[15, 30, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="params.totalElements"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.scrollbar {
  // padding-top: 20px;
  // padding: 20px;
  // height: calc(100vh - 181px);
  height: calc(100vh - 346px);

  background-color: #fff;
}
// :deep(.el-table .cell) {
//   padding: 0;
// }
.containers {
  box-sizing: border-box;
  // width: calc(100% - 48px);
  // height: calc(100vh - 48px);
  // padding: 24px;
  // overflow-y: auto;
  // background: #fff;
  .search {
    padding: 20px 20px 2px 20px;
    background-color: #fff;
    margin-bottom: 20px;
    .con_search {
      display: flex;
      align-items: center;
      width: 100%;
      height: fit-content;
      // .input_width {
      //   width: 200px;
      // }
    }
  }
  .main {
    background: #fff;
    padding: 20px;
    box-sizing: border-box;
    height: 100%;
    // background-color: #bd2727a2;
  }

  .con_table {
    // width: calc(100% - 25px);
    // height: 500px;

    // margin-left: 25px;
    overflow-y: auto;
  }

  .con_pagination {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    margin-top: 20px;
  }

  .option {
    display: flex;

    .btnse {
      display: flex;
      // margin-left: 16px;
      color: #409eff;
      cursor: pointer;

      .nofreeze {
        color: #f56c6c;
        cursor: pointer;
      }
    }
  }
}
// :deep(.containers .con_search .el-form--inline .el-form-item){
//   margin-right: 32px !important;
// }
.el-form-item {
  margin-right: 32px !important;
}
</style>
