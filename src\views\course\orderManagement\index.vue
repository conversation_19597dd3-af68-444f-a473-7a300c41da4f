<script setup>
import { onMounted, ref, watch, onActivated, nextTick } from "vue";
import { formatTime } from "@/utils/index";
import { ordersFindAll, confirmRefund } from "@/api/orderManagement";
import { requestTo } from "@/utils/http/tool";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
defineOptions({
  name: "OrderManagement"
});
onActivated(() => {
  getTableList();
});
onMounted(() => {
  getTableList();
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);
});

// 计算表格高度
const searchFormHeight = ref(0);
const tableHeight = ref("calc(100vh - 400px)");

const calculateTableHeight = async () => {
  await nextTick();
  const searchForm = document.querySelector(".con_search");
  if (searchForm) {
    searchFormHeight.value = searchForm.offsetHeight;
    // 动态计算表格高度，减去表单高度、页面其他元素高度和边距
    tableHeight.value = `calc(100vh - 277px - ${searchFormHeight.value}px)`;
  }
};

const router = useRouter();
const radio = ref(false);
const groupBuying = ref(false);
const radioGroup = val => {
  if (groupBuying.value === val) return;
  groupBuying.value = val;
  getTableList();
};
// 搜索表单
const form = ref({
  startTime: "",
  endTime: "",
  name: "",
  ordersId: "",
  courseName: "",
  orderStatus: "",
  refundStatus: ""
});
// 表格数据
const tableData = ref([]);
const params = ref({
  page: 1,
  size: 15,
  sort: "createdAt,desc",
  totalElements: 0
});
const courseTypeoptions = ref([
  {
    value: "",
    label: "全部"
  },
  {
    value: "UNPAID",
    label: "未支付"
  },
  {
    value: "PAID",
    label: "已支付"
  },
  {
    value: "PAY_FAIL",
    label: "支付失败"
  },
  {
    value: "CANCELLED",
    label: "已取消"
  },
  {
    value: "COMPLETED",
    label: "已完成"
  }
]);

const refundStatusOptions = ref([
  {
    value: "",
    label: "全部"
  },
  {
    value: "APPLICATION",
    label: "申请退款"
  },
  {
    value: "REFUNDING",
    label: "退款中"
  },
  {
    value: "REFUNDED",
    label: "已退款"
  },
  {
    value: "REFUND_REJECT",
    label: "退款驳回"
  },
  {
    value: "REFUND_FAILED",
    label: "退款失败"
  },
  {
    value: "PARTIAL_REFUND",
    label: "部分退款"
  },
  {
    value: "PARTIAL_CANCEL",
    label: "部分取消"
  },
  {
    value: "CANCEL",
    label: "取消"
  }
]);

const orderStatusTypeoptions = {
  UNPAID: "未支付",
  PAID: "已支付",
  PAY_FAIL: "支付失败",
  CANCELLED: "已取消",
  COMPLETED: "已完成",
  PAY: "支付中",
  REFUNDING: "退款中",
  REFUNDED: "已退款",
  PARTIALLY_REFUNDED: "部分退款",
  APPLICATION: "申请退款",
  REFUND_REJECT: "退款驳回",
  REFUND_FAILED: "退款失败",
  PARTIAL_REFUND: "部分退款",
  PARTIAL_CANCEL: "部分取消",
  CANCEL: "取消"
};

// 状态颜色映射
const getStatusColor = status => {
  const colorMap = {
    // 红色 - 申请退款、退款驳回、退款失败、支付失败
    APPLICATION: "#FF6161",
    REFUND_REJECT: "#FF6161",
    REFUND_FAILED: "#FF6161",
    PAY_FAIL: "#FF6161",

    // 橙色 - 退款中
    REFUNDING: "#FF8000",

    // 蓝色 - 已退款、部分退款、部分取消、已支付、已完成
    REFUNDED: "#4095E5",
    PARTIAL_REFUND: "#4095E5",
    PARTIALLY_REFUNDED: "#4095E5",
    PARTIAL_CANCEL: "#4095E5",
    PAID: "#4095E5",
    COMPLETED: "#4095E5",

    // 灰色 - 未支付、已取消、支付中、取消
    UNPAID: "#9A9A9A",
    CANCELLED: "#9A9A9A",
    PAY: "#9A9A9A",
    CANCEL: "#9A9A9A"
  };

  return colorMap[status] || "#9A9A9A"; // 默认灰色
};
// 获取列表信息
const getListLoading = ref(false);
const getTableList = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort,
    groupBuying: groupBuying.value
  };
  for (const paramsDataKey in form.value) {
    let isArray = Array.isArray(form.value[paramsDataKey]);
    if (isArray) {
      if (form.value[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    } else {
      if (form.value[paramsDataKey]) {
        paramsData[paramsDataKey] = form.value[paramsDataKey];
      }
    }
  }
  // console.log("🍧-----paramsData-----", paramsData);
  // const [err, result] = await requestTo(ordersFindAll(paramsData));
  // // console.log("🎁-----result-----", result);
  // if (result) {
  //   tableData.value = result?.content;

  //   params.value.totalElements = result.totalElements;
  // } else {
  //   ElMessage.error(err);
  // }
  try {
    const { code, data, msg } = await ordersFindAll(paramsData);
    // console.log("🎁-----data-----", code, data, msg);
    if (code == 200) {
      tableData.value = data?.content;
      params.value.totalElements = data?.totalElements;
      await nextTick();
      calculateTableHeight();
    }
  } catch (error) {
    console.log("catch-----error-----", error);
  }
  getListLoading.value = false;
};
//分页
const handleCurrentChange = e => {
  params.value.page = e;
  getTableList();
};
const handleSizeChange = val => {
  params.value.page = 1;
  params.value.size = val;
  getTableList();
};
// 查看详情
const getId = id => {
  router.push({ path: "/course/orderManagement/orderDetails", query: { id } });
}; //搜索
const searchData = () => {
  params.value.page = 1;
  getTableList();
};
// 重置
const setData = () => {
  form.value = {
    startTime: "",
    endTime: "",
    name: "",
    ordersId: "",
    courseName: "",
    orderStatus: "",
    refundStatus: ""
  };
  params.value.page = 1;
  value1.value = [];
  getTableList();
};
// 选择时间
const timeChange = async value => {
  if (!value || value.length !== 2) return;

  // 开始时间（默认 00:00:00）
  form.value.startTime = new Date(value[0]).getTime();

  // 结束时间（设置为 23:59:59.999）
  const endDate = new Date(value[1]);
  endDate.setHours(23, 59, 59, 999); // 关键修改
  form.value.endTime = endDate.getTime();
  await nextTick();
  calculateTableHeight();
};

const value1 = ref([]);
// 前往创建
const goSet = () => {
  router.push({
    path: "/course/courseCreate",
    query: {
      type: "create"
    }
  });
  // console.log('🍭-----goSet-----', goSet);
};
const Freeze = async row => {
  console.log("确认退单");
  ElMessageBox.confirm("你确定要退单吗?", "确认退单", {
    confirmButtonText: "确认退单",
    cancelButtonText: "取消",
    type: ""
  })
    .then(() => {
      isChargebackApi(row);
    })
    .catch(() => {
      // ElMessage({
      //   type: "info",
      //   message: "已取消"
      // });
    });
};
const isChargebackApi = async (row, bool) => {
  // console.log("🦄-----row-----", row);
  const params = {
    id: row.orderNo
  };
  // console.log("🦄-----params-----", params);
  // return;
  const { code } = await confirmRefund(params);
  if (code === 200) {
    ElMessage({
      type: "success",
      message: "退单成功"
    });
    getTableList();
  } else {
    ElMessage({
      type: "error",
      message: "退单失败"
    });
  }
};
</script>

<template>
  <div class="page-container">
    <div class="containers">
      <!-- <div class="con_top">
        <div class="titles">订单管理</div>
        <el-button type="primary" @click="goSet">创建课程</el-button>
      </div> -->

      <div class="con_search">
        <el-form :model="form" label-width="70px" :inline="true">
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="value1"
              type="daterange"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              @change="timeChange"
            />
          </el-form-item>
          <el-form-item label="订单号">
            <el-input
              v-model="form.orderNo"
              class="input_width"
              placeholder="请输入"
              clearable
            />
          </el-form-item>
          <el-form-item label="课程名">
            <el-input
              v-model="form.courseName"
              class="input_width"
              placeholder="请输入"
              clearable
            />
          </el-form-item>
          <el-form-item label="购买人" class="refund_status">
            <el-input
              v-model="form.name"
              class="input_width"
              placeholder="请输入"
              clearable
            />
          </el-form-item>
          <el-form-item label="订单状态" class="refund_status">
            <el-select
              v-model="form.orderStatus"
              style="width: 200px"
              placeholder="请选择"
              :empty-values="[null, undefined]"
              :value-on-clear="null"
            >
              <el-option
                v-for="item in courseTypeoptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="退款状态" class="refund_status">
            <el-select
              v-model="form.refundStatus"
              style="width: 200px"
              placeholder="请选择"
              :empty-values="[null, undefined]"
              :value-on-clear="null"
            >
              <el-option
                v-for="item in refundStatusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label=" ">
            <div class="flex">
              <el-button type="primary" @click="searchData">搜索</el-button>
              <el-button @click="setData">重置</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div class="main_content">
        <el-radio-group v-model="radio" style="margin-bottom: 20px">
          <el-radio-button :value="false" @click="radioGroup(false)">
            普通订单
          </el-radio-button>
          <el-radio-button :value="true" @click="radioGroup(true)">
            团购订单
          </el-radio-button>
        </el-radio-group>
        <el-scrollbar :style="{ height: tableHeight }">
          <div class="con_table">
            <el-table
              class="table"
              :data="tableData"
              show-overflow-tooltip
              :max-height="tableHeight"
              :header-cell-style="{
                backgroundColor: '#fafafa',
                color: '#565353'
              }"
            >
              <el-table-column prop="ordersId" label="订单号" align="left">
                <template #default="scope">
                  <div>
                    {{ scope.row.orderNo || "--" }}
                  </div>
                </template>
              </el-table-column>
              <!-- <el-table-column
                prop="organizationName"
                label="子订单号"
                align="left"
              >
                <template #default="scope">
                  <div>
                    {{ scope.row.organizationName || "--" }}
                  </div>
                </template>
              </el-table-column> -->
              <el-table-column width="200px" prop="createdAt" label="创建时间">
                <template #default="scope">
                  <div>
                    {{
                      formatTime(scope.row?.createdAt, "YYYY-MM-DD HH:mm:ss") ||
                      "暂无"
                    }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="courseName" label="课程名" min-width="120">
                <template #default="scope">
                  <el-text class="" truncated>
                    {{ scope.row.courseName || "--" }}
                  </el-text>
                </template>
              </el-table-column>
              <el-table-column
                prop="termNumber"
                label="课程期号"
                min-width="120"
              >
                <template #default="scope">
                  <el-text class="" truncated>
                    {{ scope.row.termNumber }}
                  </el-text>
                </template>
              </el-table-column>
              <el-table-column prop="buyer" label="购买人" align="left">
                <template #default="scope">
                  <div>
                    {{ scope.row.buyer || "--" }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="orderStatus" label="订单状态" align="left">
                <template #default="scope">
                  <div
                    class="status-text"
                    :style="{ color: getStatusColor(scope.row.orderStatus) }"
                  >
                    {{ orderStatusTypeoptions[scope.row.orderStatus] || "--" }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                prop="refundStatus"
                label="退款状态"
                align="left"
              >
                <template #default="scope">
                  <div
                    class="status-text"
                    :style="{ color: getStatusColor(scope.row.refundStatus) }"
                  >
                    {{ orderStatusTypeoptions[scope.row.refundStatus] || "--" }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                fixed="right"
                width="140px"
                label="操作"
                align="center"
              >
                <template #default="scope">
                  <div class="box">
                    <div
                      v-code="['625']"
                      class="btnse"
                      @click="getId(scope.row.id)"
                    >
                      详情
                    </div>
                    <!-- <div
                      v-if="scope.row.orderStatus === 'REFUNDING'"
                      class="btnse"
                      @click="Freeze(scope.row)"
                    >
                      退单
                    </div> -->
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-scrollbar>

        <div class="con_pagination">
          <!-- 分页 -->
          <el-pagination
            v-model:current-page="params.page"
            v-model:page-size="params.size"
            class="pagination"
            background
            :page-sizes="[15, 30, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="params.totalElements"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.page-container {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.containers {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  .con_top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: fit-content;
    margin-bottom: 20px;
    .titles {
      font-size: 20px;
      font-weight: bold;
      color: #606266;
    }
  }

  .con_search {
    display: flex;
    align-items: center;
    width: 100%;
    height: fit-content;
    margin-bottom: 24px;
    background: #fff;
    padding: 20px 20px 0 20px;
    .el-form--inline .el-form-item {
      margin-right: 0;
    }
    .input_width {
      width: 200px;
    }
  }

  .refund_status {
    width: 280px;
  }

  .main_content {
    background: #fff;
    padding: 20px 20px 2px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
    .con_table {
      .btnse {
        color: #409eff;
        cursor: pointer;
      }
    }
    .con_pagination {
      display: flex;
      justify-content: flex-end;
      width: 100%;
      padding: 16px 0px;
      background-color: #fff;
    }
  }
}
.box {
  display: flex;
  justify-content: center;
  :nth-child(2) {
    margin-left: 20px;
  }
}
.table {
  // margin-top: 20px;
  display: block;
  // height: 500px;
}

.status-text {
  font-weight: 500;
  font-size: 14px;
}
</style>
